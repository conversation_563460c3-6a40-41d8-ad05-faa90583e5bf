import os
import io
import uuid
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from datetime import datetime, timezone
from dotenv import load_dotenv
from markdownify import MarkdownConverter
from requests.auth import HTTPBasicAuth
import logging
import hashlib
import urllib.parse
import core.common as common_util
import re
from business.services.http_client import HttpClient
from typing import Optional, Dict, List
from requests.cookies import create_cookie
import urllib.parse

load_dotenv()
logger = logging.getLogger(__name__)

CONFLUENCE_URL = os.getenv("CONFLUENCE_URL")
CONFLUENCE_SPACE_KEY = os.getenv("CONFLUENCE_SPACE_KEY")
CONFLUENCE_TOKEN = os.getenv("CONFLUENCE_TOKEN")
CONFLUENCE_EMAIL = os.getenv("CONFLUENCE_EMAIL")
NEXUS_DOCS_PUBLIC_URL = os.getenv("NEXUS_DOCS_PUBLIC_URL", "https://dev-nexus-build.vnggames.net/aawp/nexus-docs?pages={{page_id}}")

http_client = HttpClient()

class CustomMarkdownConverter(MarkdownConverter):
    def convert_img(self, el, text, parent_tags):
        """
        Customize image conversion to ensure Markdown image syntax ![]()
        """
        alt = el.get('alt', '')
        src = el.get('src', '')
        return f'![{alt}]({src})\n\n' if src else ''

def process_html_and_upload_images(html: str, minio_client, cookies: Optional[List[Dict[str, str]]] = None):
    """
    Process HTML content:
    1. Parse HTML with BeautifulSoup
    2. Find all img tags
    3. Download images from original src
    4. Upload images to MinIO
    5. Replace src attributes with MinIO URLs
    
    Returns processed HTML with updated image sources
    """
    soup = BeautifulSoup(html, "html.parser")
    public_endpoint = os.getenv('MINIO_PUBLIC_ENDPOINT', 'http://localhost:9000')
    public_bucket = os.getenv('MINIO_PUBLIC_BUCKET_NAME', 'public')
    
    
    # replace src with minio url
    for img in soup.find_all("img"):
        src_url = img.get("src")
        if not src_url:
            print("Skipping: No src attribute found")
            continue
        
        # Skip already processed URLs (pointing to our MinIO)
        if public_endpoint in src_url:
            print(f"Skipping: Already processed URL {src_url}")
            continue
        
        # Fill confluence url if image src is relative url
        if src_url.startswith(f"/wiki/spaces/{CONFLUENCE_SPACE_KEY}/pages"):
            src_url = f"{CONFLUENCE_URL}{src_url}"
        
        session = requests.Session()
        try:
            # Download image from original source
            
            logger.info(f"Downloading image from {src_url}")
            if cookies:
                cookie_dict = {}
                for c in cookies:
                    cookie_dict[c['name']] = c['value']
                session.cookies.update(cookie_dict)
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'image/*',
                'Cache-Control': 'no-cache'
            }
            
            response = session.get(
                src_url, 
                auth=HTTPBasicAuth(CONFLUENCE_EMAIL, CONFLUENCE_TOKEN), 
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            
            # Kiểm tra content-type
            content_type = response.headers.get("Content-Type", "").lower()
            if not content_type.startswith("image/"):
                print(f"Skipping {src_url}: Not an image (Content-Type: {content_type})")
                continue
            
            img_data = response.content
            # hash image data to generate filename
            hash_content = common_util.md5_hash(img_data)
            
            # Generate a unique filename
            original_filename = os.path.basename(urlparse(src_url).path)
            if not original_filename:
                original_filename = "image.jpg"  # Default filename if path has no basename
            
            
            decoded = urllib.parse.unquote(original_filename)
            replaced_filename = decoded.replace(" ", "_")
            filename = f"images/{hash_content}-{replaced_filename}"
            
            # Upload image to MinIO
            img_data_io = io.BytesIO(img_data)
            img_data_io.seek(0)  # Đặt con trỏ về đầu
            img_size = len(img_data)
            
            minio_client.put_object(
                bucket_name=public_bucket,
                object_name=filename,
                data=img_data_io,
                length=img_size,
                content_type=content_type
            )
            
            # Replace src with MinIO URL
            new_url = f"{public_endpoint}/{public_bucket}/{filename}"
            img["src"] = new_url
            
            print(f"Successfully processed image {src_url} -> {new_url}")
            
        except Exception as e:
            print(f"Error processing image {src_url}: {e}")
            continue
        finally:
            if session:
                session.close()
    
    # replace src pattern CONFLUENCE_URL to MINIO_PUBLIC_ENDPOINT
    # Ex.: https://vnggames.atlassian.net/wiki/spaces/NKB/pages/589562426/Example
    # -> https://dev-nexus-build.vnggames.net/aawp/nexus-docs?pages=589562426
    pattern = re.compile(
        re.escape(f"{CONFLUENCE_URL}/wiki/spaces/NKB/pages/") + r"(\d+)"
    )

    for tag in soup.find_all("a", href=True):
        match = pattern.search(tag["href"])
        if match:
            page_id = match.group(1)
            tag["href"] = NEXUS_DOCS_PUBLIC_URL.format(page_id=page_id)
    
    # Return processed HTML
    return str(soup)


def save_processed_html_to_minio(html: str, minio_client, bucket: str, content_type: str, cookies: Optional[List[Dict[str, str]]] = None):
    """
    Process HTML to handle images and save to MinIO
    """
    # Process HTML to upload images and update src attributes
    if html:
        logger.info("Processing HTML to upload images and update src attributes")
        processed_html = process_html_and_upload_images(html, minio_client, cookies)
        logger.debug(f"Processing HTML to upload images: {processed_html}")
        
        processed_html = process_html_and_upload_attachment(processed_html, minio_client, cookies)
        logger.debug(f"Processing HTML to upload attachment: {processed_html}")
                
        logger.debug("Processing HTML to upload images and update src attributes")
        now_str = datetime.now(timezone.utc).isoformat()

        if content_type == "html":
            logger.info("Saving HTML to MinIO")
            filename = f"{uuid.uuid4().hex[:8]}.html"
            file_content = processed_html
        elif content_type == "markdown":
            logger.info("Saving Markdown to MinIO")
            filename = f"{uuid.uuid4().hex[:8]}.md"
            file_content = CustomMarkdownConverter().convert(processed_html)
        else:
            logger.error(f"Unsupported content type: {content_type}")
            raise ValueError(f"Unsupported content type: {content_type}")
        logger.debug(f"File content: {file_content}")
        file_data = io.BytesIO(file_content.encode('utf-8'))
        file_size = file_data.getbuffer().nbytes
        
        now_str = datetime.now(timezone.utc).isoformat()
    else:
        logger.info("Saving empty HTML to MinIO")
        file_data = io.BytesIO(b"")
        file_size = 0
        now_str = datetime.now(timezone.utc).isoformat()
        filename = f"{uuid.uuid4().hex[:8]}.html"
    
    # Xác định content_type dựa trên loại file
    actual_content_type = "text/html"
    if content_type == "markdown":
        actual_content_type = "text/markdown"
    
    logger.info(f"Saving file to MinIO: {filename}")
    # Save to MinIO
    minio_client.put_object(
        bucket_name=bucket,
        object_name=filename,
        data=file_data,
        length=file_size,
        content_type=actual_content_type,
        metadata={
            "created_date": now_str,
            "updated_date": now_str,
            "content_type": actual_content_type
        }
    )
    return filename, {
        "uri": filename,
        "content_type": actual_content_type,
        "created_date": now_str,
        "updated_date": now_str
    }

def replace_src_in_html(html_content, src_map):
    soup = BeautifulSoup(html_content, 'html.parser')
    for tag in soup.find_all(src=True):
        old_src = tag['src']
        if old_src in src_map:
            tag['src'] = src_map[old_src]
    return str(soup)

def convert_confluence_preview_to_download_url(preview_url: str) -> str:
    """
    Convert a Confluence preview URL to a direct download link.
    """
    
    parsed = urllib.parse.urlparse(preview_url)
    query = urllib.parse.parse_qs(parsed.query)
    
    preview_path = urllib.parse.unquote(query["preview"][0]) 
    path_parts = preview_path.strip("/").split("/")  
    
    if len(path_parts) < 3:
        raise ValueError("Preview path does not contain expected format.")
    
    page_id = path_parts[0]
    filename = path_parts[-1]

    base_url = f"{parsed.scheme}://{parsed.netloc}"
    download_url = f"{base_url}/wiki/download/attachments/{page_id}/{filename}"

    return download_url

def process_html_and_upload_attachment(html: str, minio_client, cookies):
    public_endpoint = os.getenv('MINIO_PUBLIC_ENDPOINT', 'http://localhost:9000')
    public_bucket = os.getenv('MINIO_PUBLIC_BUCKET_NAME', 'public')
    soup = BeautifulSoup(html, "html.parser")

    for a_tag in soup.find_all("a", href=True):
        href = a_tag["href"]

        # Trường hợp 1: file trong attachments
        
        # just process link begin by /wiki/spaces/{CONFLUENCE_SPACE_KEY}/pages/{page_id}
        is_attachment_link = (
            href.startswith(f"/wiki/spaces/{CONFLUENCE_SPACE_KEY}/pages/")
        )

        if is_attachment_link :
            logger.debug("Processing PDF/TXT file: %s", href)
            link = f"{CONFLUENCE_URL}{href}"

            session = None

            try:
                if "preview=" in href:
                    download_url = convert_confluence_preview_to_download_url(link)
                elif "/viewpageattachments.action" in href:
                    download_url = convert_confluence_preview_to_download_url(link)
                else:
                    # Link đã là direct PDF link, không cần convert
                    download_url = link
                logger.info(f"Downloading file from {download_url}")

                session = requests.Session()
                if cookies:
                    cookie_dict = {c['name']: c['value'] for c in cookies}
                    session.cookies.update(cookie_dict)

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                    'Accept': '*/*',
                    'Cache-Control': 'no-cache'
                }

                response = session.get(
                    download_url,
                    auth=HTTPBasicAuth(CONFLUENCE_EMAIL, CONFLUENCE_TOKEN),
                    headers=headers,
                    timeout=15
                )
                response.raise_for_status()

                content_type = response.headers.get("Content-Type", "").lower()
                file_data = response.content
                hash_content = common_util.md5_hash(file_data)

                original_filename = os.path.basename(urlparse(download_url).path)
                decoded = urllib.parse.unquote(original_filename).replace(" ", "_")
                filename = f"attachments/{hash_content}-{decoded}"

                file_io = io.BytesIO(file_data)
                file_io.seek(0)
                file_size = len(file_data)

                minio_client.put_object(
                    bucket_name=public_bucket,
                    object_name=filename,
                    data=file_io,
                    length=file_size,
                    content_type=content_type
                )

                new_url = f"{public_endpoint}/{public_bucket}/{filename}"
                a_tag["href"] = new_url
                logger.info(f"✅ Uploaded file to MinIO: {new_url}")

            except Exception as e:
                logger.error(f"❌ Error processing {link}: {e}")
            finally:
                if session:
                    session.close()
    return str(soup)
