from fastapi import Request, APIRouter
from fastapi.responses import JSONResponse
# Sử dụng module mới
from data.db_connection import get_session
import logging as logger
# get_service
from business.services.user_service import UserService
router = APIRouter()

@router.get('/api/content/{page_id}')
# Route GET /api/content/<page_id>
#  └── gọi UserService.get_page_by_page_id(page_id)
#       └── gọi page_repository2.get_page_by_page_id
#       └── trả về thông tin page + content_uri
#  └── dùng MinIO để lấy file HTML từ bucket theo content_uri
#  └── trả Response về client
async def get_html_content_by_page_id(request: Request, page_id: str):
    # L<PERSON>y dữ liệu từ DB
    auth_token = request.headers.get("Authorization")
    if not auth_token:
        return JSONResponse(content={"error": "Authorization token is required"}, status_code=401)
    try:
        db = request.state.db
        service = UserService(db)
        try:
            content = service.get_page_by_page_id(authorization=auth_token, page_id=page_id)
        except ValueError as e:
            return JSONResponse(content={"error": str(e)}, status_code=404)
        if not content:
            return JSONResponse(content={"error": "Page not found"}, status_code=404)
        # json datetime
        content['created_date'] = content['created_date'].isoformat()
        content['updated_date'] = content['updated_date'].isoformat()
        uri_html = content.get("html_uri")
        # Lấy MinIO client từ app context
        minio_client = request.app.state.minio_client
        bucket = request.app.state.minio_bucket
        try:
            response = minio_client.get_object(bucket, uri_html)
            html_content = response.read()
            response.close()
            response.release_conn()
            # extend content
            content['content'] = html_content.decode('utf-8')
            content['content_type'] = "text/html"
            return JSONResponse(content)
        except Exception as err:
            return JSONResponse(content={"error": "Error fetching file from MinIO", "message": str(err)}, status_code=500)
    except ValueError as e:
        return JSONResponse(content={"error": str(e)}, status_code=400)
    
@router.get('/api/list_pages')
# GET /api/list_pages?parent_id=3 Authorization: Bearer xyz123
async def list_pages(request: Request):
    auth_token = request.headers.get('Authorization')
    if not auth_token:
        return JSONResponse(content={"error": "Authorization token is required"}, status_code=401)
    try:
        db = request.state.db
        service = UserService(db)
        try:
            # page from request
            parent_id = request.query_params.get('parent_id', None)
            pages = service.list_pages(authorization=auth_token, parent_id = parent_id)
            # convert datetime to string
            for page in pages:
                page['created_date'] = page['created_date'].isoformat()
                page['updated_date'] = page['updated_date'].isoformat()
        except ValueError as e:
            return JSONResponse(content={"error": str(e)}, status_code=404)
        if not pages:
            return JSONResponse(content={"message": "No pages found"}, status_code=200)
        return JSONResponse(content=pages)
    except ValueError as e:
        return JSONResponse(content={"error": str(e)}, status_code=400)
    except Exception as e:
        logger.error(f"Failed to fetch pages: {str(e)}")
        return JSONResponse(content={"error": "Failed to fetch pages", "message": str(e)}, status_code=500)  
    
