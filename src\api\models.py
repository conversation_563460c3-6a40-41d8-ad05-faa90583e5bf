from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class ErrorResponse(BaseModel):
    detail: str

class PageResponse(BaseModel):
    id: int
    title: str
    html_uri: str
    created_date: datetime
    updated_date: datetime
    status: str

class PublishResponse(BaseModel):
    message: str
    page_id: int
    title: str
    html_uri: str

class PublishRequest(BaseModel):
    token: str
    email: str
    domain: str
    page_id: str
