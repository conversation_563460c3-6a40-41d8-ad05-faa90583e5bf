from typing import TypedDict, Optional
from datetime import datetime


class SpaceDTO(TypedDict):
    """Data Transfer Object for Space entity"""
    id: int
    space_id: str
    space_key: str
    space_name: str
    collection_id: str
    created_date: datetime
    updated_date: datetime


class PageDTO(TypedDict):
    """Data Transfer Object for Page entity"""
    id: int
    page_id: str
    title: Optional[str]
    parent_id: Optional[str]
    space_id: Optional[str]
    html_uri: Optional[str]
    created_date: datetime
    updated_date: datetime
    status: Optional[str]
    content_type: Optional[str]
    is_published: bool
    type: str
    created_by: Optional[str]
    last_updated_by: Optional[str]
    last_approved_date: Optional[datetime]


class SpaceCreateDTO(TypedDict):
    """Data Transfer Object for creating a new Space"""
    space_id: str
    space_key: str
    space_name: str
    collection_id: str


class SpaceUpdateDTO(TypedDict, total=False):
    """Data Transfer Object for updating a Space (all fields optional)"""
    space_name: str
    collection_id: str
    space_key: str


class PageCreateDTO(TypedDict, total=False):
    """Data Transfer Object for creating a new Page"""
    page_id: str
    title: str
    type: str
    is_published: bool
    content_type: Optional[str]
    parent_id: Optional[str]
    space_id: Optional[str]
    space_key: Optional[str]
    html_uri: Optional[str]
    created_by: Optional[str]
    status: Optional[str]


class PageUpdateDTO(TypedDict, total=False):
    """Data Transfer Object for updating a Page (all fields optional)"""
    title: str
    type: str
    is_published: bool
    content_type: Optional[str]
    parent_id: Optional[str]
    space_id: Optional[str]
    space_key: Optional[str]
    html_uri: Optional[str]
    status: Optional[str]
    last_updated_by: Optional[str]
    last_approved_date: Optional[datetime]
