from sqlalchemy.orm import Session
from data.repositories.models import Page
from datetime import datetime
from typing import List, Optional, Dict, Any
from functools import lru_cache
import logging
from data.db_connection import get_session
import roman
import threading

logger = logging.getLogger(__name__)

def insert_page(db: Session, page_data: Dict[str, Any]) -> int:
    try:
        page = Page(**page_data)
        db.add(page)
        db.commit()
        db.refresh(page)
        return page.id
    except Exception as e:
        db.rollback()
        raise e

def insert_page(db: Session, page: Page) -> int:
    try:
        db.add(page)
        db.commit()
        db.refresh(page)
        return page.id
    except Exception as e:
        db.rollback()
        raise e


def update_page(db: Session, page_id: str, **kwargs) -> int:
    try:
        kwargs['updated_date'] = datetime.now()
        set = db.query(Page).filter(Page.page_id == page_id).update(kwargs)
        db.commit()
        return set
    except Exception as e:
        db.rollback()
        raise e

def get_page_by_page_id(db: Session, page_id: str) -> Optional[Page]:
    page = db.query(Page).filter(Page.page_id == page_id).first()
    if page:
        return page
    logger.warning(f"Page not found for page_id {page_id}")
    return None

def is_page_exists(db: Session, page_id: str) -> bool:
    return db.query(Page).filter(Page.page_id == page_id).count() > 0

@lru_cache(maxsize=4000)
def _get_roman_numerals():
    """Cache all valid roman numerals once"""
    return {roman.toRoman(i) for i in range(1, 4000)}

def _smart_sort_key(item):
    try:
        title = item["title"] if isinstance(item, dict) else item.title
        if not title:
            return float('inf')
        
        # Extract leading digits
        if title[0].isdigit():
            num_str = ''
            for char in title:
                if char.isdigit():
                    num_str += char
                else:
                    break
            return int(num_str) if num_str else float('inf')
        
        # Roman numeral logic
        words = title.split()
        if words:
            first_word = words[0].rstrip('.')
            if first_word.upper() in _get_roman_numerals():
                return roman.fromRoman(first_word.upper())
        
        return float('inf')
    except:
        return float('inf')

def sort_children_recursive(items):
    """Sort items and their children recursively"""
    items.sort(key=_smart_sort_key)
    for item in items:
        if item.get("children"):
            sort_children_recursive(item["children"])

def get_tree_pages(space_id: str, published_only: Optional[bool] = True) -> List[Dict[str, Any]]:
    tree = []
    lookup = {}
    
    logger.info("Get tree pages from database...")

    if not space_id:
        logger.warning("Space ID is not provided")
        return []

    space_id_str = str(space_id)
    
    pages = []
    with get_session() as session:
        pages = session.query(Page).order_by(Page.title).filter(Page.is_published == published_only, Page.space_id == space_id_str).all()
    try:
        if not pages:
            return []
        
        # Build lookup
        for page in pages:
            lookup[page.page_id] = {
                "page_id": page.page_id,
                "title": page.title,
                "status": page.status,
                "type": page.type,
                "parent_id": page.parent_id,
                "is_published": page.is_published,
                "content_type": page.content_type,
                "children": []
            }

        # Build tree
        for page in pages:
            node = lookup[page.page_id]
            if page.parent_id in (None, 0):
                tree.append(node)
            else:
                parent = lookup.get(page.parent_id)
                if parent:
                    parent["children"].append(node)
                else:
                    logger.warning(f"Orphaned node {page.page_id}")
                    tree.append(node)

        # Sort tree recursively
        sort_children_recursive(tree)
        
        # Remove folder have no children
        _remove_only_leaf_folders(tree, is_root_level=True)
        return tree
    except Exception as e:
        logger.exception(f"Error getting tree pages {space_id}: {e}")
        raise e
        
def _remove_only_leaf_folders(tree, is_root_level=True):
    """
    Xóa các folder lá (leaf folders) không có children hoặc chỉ có children là folder rỗng
    
    Args:
        tree: Danh sách các node trong cây
        is_root_level: Có phải là cấp cao nhất không (mặc định là True)
    """
    if not tree:
        return
    
    # Đệ quy xuống children trước
    for page in list(tree):  # Dùng list() để tạo bản sao tránh lỗi khi thay đổi tree
        if page.get("children"):
            _remove_only_leaf_folders(page["children"], is_root_level=False)
    
    # Nếu là cấp cao nhất, không xóa folder
    if is_root_level:
        return
    
    # Xóa folder lá ở level hiện tại
    folders_to_remove = []
    for page in tree:
        # Chỉ xóa folder nếu nó không có children hoặc children rỗng
        if page.get("type") == "folder" and (
            page.get("children") is None or 
            len(page.get("children", [])) == 0
        ):
            folders_to_remove.append(page)
    
    for folder in folders_to_remove:
        tree.remove(folder)

def delete_page(db: Session, page_id: str) -> None:
    try:
        db.query(Page).filter(Page.page_id == page_id).delete()
        db.commit()
    except Exception as e:
        db.rollback()
        raise e
    
def get_all_pages(db: Session, parent_id: Optional[int]) -> List[Dict[str, Any]]:
    if parent_id is not None:
        query = db.query(Page).filter(Page.parent_id == parent_id)
    else:
        query = db.query(Page).filter(Page.parent_id.is_(None))
    return [
        {
            "id": page.id,
            "page_id": page.page_id,
            "space_id": page.space_id,
            "title": page.title,
            "parent_id": page.parent_id,
            "html_uri": page.html_uri,
            "created_date": page.created_date,
            "updated_date": page.updated_date,
            "status": page.status
        } for page in query.all()
    ]
