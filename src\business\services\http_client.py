import requests
from typing import Optional, Dict, Any, Union
from urllib.parse import urljoin
import logging

logger = logging.getLogger(__name__)

class HttpClient:
    """
    A simple HTTP client wrapper around requests.Session
    
    Provides a convenient interface for making HTTP requests with
    automatic base URL handling, session management, and error handling.
    """
    
    def __init__(
        self, 
        base_url: Optional[str] = None, 
        headers: Optional[Dict[str, str]] = None, 
        cookies: Optional[Dict[str, str]] = None, 
        timeout: int = 10,
        verify_ssl: bool = True,
        max_retries: int = 0
    ):
        """
        Initialize the HTTP client
        
        Args:
            base_url: Base URL for all requests
            headers: Default headers to include in all requests
            cookies: Default cookies to include in all requests
            timeout: Request timeout in seconds
            verify_ssl: Whether to verify SSL certificates
            max_retries: Number of retry attempts for failed requests
        """
        self.session = requests.Session()
        self.base_url = base_url.rstrip('/') + '/' if base_url else ""
        self.timeout = timeout
        
        # Set up session configuration
        if headers:
            self.session.headers.update(headers)
        if cookies:
            self.session.cookies.update(cookies)
            
        self.session.verify = verify_ssl
        
        # Set up retry strategy if specified
        if max_retries > 0:
            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry
            
            retry_strategy = Retry(
                total=max_retries,
                status_forcelist=[408, 429, 500, 502, 503, 504],
                method_whitelist=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"],
                backoff_factor=1
            )
            adapter = HTTPAdapter(max_retries=retry_strategy)
            self.session.mount("http://", adapter)
            self.session.mount("https://", adapter)
    
    def _full_url(self, url: str) -> str:
        """Construct full URL from base URL and endpoint"""
        if not url:
            return self.base_url
        
        # Handle absolute URLs
        if url.startswith(('http://', 'https://')):
            return url
            
        # Use urljoin for proper URL construction
        return urljoin(self.base_url, url.lstrip('/'))
    
    def _make_request(
        self, 
        method: str, 
        url: str, 
        **kwargs
    ) -> requests.Response:
        """
        Make HTTP request with common error handling
        
        Args:
            method: HTTP method (GET, POST, etc.)
            url: URL endpoint
            **kwargs: Additional arguments passed to requests
            
        Returns:
            requests.Response object
            
        Raises:
            requests.RequestException: For request-related errors
        """
        full_url = self._full_url(url)
        
        # Set default timeout if not provided
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
            
        try:
            logger.debug(f"Making {method} request to {full_url}")
            response = self.session.request(method, full_url, **kwargs)
            logger.debug(f"Response status: {response.status_code}")
            return response
            
        except requests.RequestException as e:
            logger.error(f"Request failed: {method} {full_url} - {str(e)}")
            raise
    
    def get(
        self, 
        url: str, 
        params: Optional[Dict[str, Any]] = None, 
        **kwargs
    ) -> requests.Response:
        """Make GET request"""
        return self._make_request('GET', url, params=params, **kwargs)
    
    def post(
        self, 
        url: str, 
        data: Optional[Union[Dict, str, bytes]] = None, 
        json: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> requests.Response:
        """Make POST request"""
        return self._make_request('POST', url, data=data, json=json, **kwargs)
    
    def put(
        self, 
        url: str, 
        data: Optional[Union[Dict, str, bytes]] = None, 
        json: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> requests.Response:
        """Make PUT request"""
        return self._make_request('PUT', url, data=data, json=json, **kwargs)
    
    def patch(
        self, 
        url: str, 
        data: Optional[Union[Dict, str, bytes]] = None, 
        json: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> requests.Response:
        """Make PATCH request"""
        return self._make_request('PATCH', url, data=data, json=json, **kwargs)
    
    def delete(self, url: str, **kwargs) -> requests.Response:
        """Make DELETE request"""
        return self._make_request('DELETE', url, **kwargs)
    
    def head(self, url: str, **kwargs) -> requests.Response:
        """Make HEAD request"""
        return self._make_request('HEAD', url, **kwargs)
    
    def options(self, url: str, **kwargs) -> requests.Response:
        """Make OPTIONS request"""
        return self._make_request('OPTIONS', url, **kwargs)
    
    def set_header(self, key: str, value: str) -> None:
        """Set a default header for all requests"""
        self.session.headers[key] = value
    
    def remove_header(self, key: str) -> None:
        """Remove a default header"""
        self.session.headers.pop(key, None)
    
    def set_cookie(self, key: str, value: str) -> None:
        """Set a cookie for all requests"""
        self.session.cookies.set(key, value)
    
    def set_auth(self, auth: tuple) -> None:
        """Set authentication for all requests"""
        self.session.auth = auth
    
    def set_bearer_token(self, token: str) -> None:
        """Set Bearer token authentication"""
        self.set_header('Authorization', f'Bearer {token}')
    
    def close(self) -> None:
        """Close the session and clean up resources"""
        self.session.close()
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()