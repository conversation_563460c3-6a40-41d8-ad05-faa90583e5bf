import requests
import os
from dotenv import load_dotenv
import logging
from config.config import settings
from fastapi import HTTPException
import data.repositories.page_repository2 as page_repo
import data.repositories.space_repository as space_repo
from typing import Optional

logger = logging.getLogger("__name__")

def sync_to_kb(page_id: str, collection_id: str, space_key: Optional[str], confluence_url: Optional[str], ref_url: Optional[str], confluence_email: Optional[str], confluence_token: Optional[str], updated_by: Optional[str]) -> bool:
    url = f"{settings.kb_publisher_service}/api/ingestor/confluence/sync"
    payload = {
        "collection_id": collection_id,
        "page_id": page_id,
        "confluence_url": confluence_url,
        "confluence_email": confluence_email,
        "confluence_token": confluence_token,
        "max_pages": 1,
        "space_id": space_key,
        "include_attachments": True,
        "ref_url": ref_url,
        "updated_by": updated_by or "nexus-docs-admin"
    }
    logger.info("Curl to KB: " + url + " with payload: " + str(payload))
    response = requests.post(
        url,
        headers={"Accept": "application/json"},
        json=payload
    )
    
    if not response:
        logger.error("Failed to sync to KB")
        raise Exception("Failed to sync to KB")

    if response.status_code != 200:
        if response.json():
            logger.error(f"Failed to sync to KB: {response.json()}")
        else:
            logger.error("Failed to sync to KB: Empty response")
        return False
    
    logger.info(f"Synced to KB page {confluence_url} - {space_key} - {page_id} successfully")
    return True

def unsync_to_kb(page_id: str, collection_id: str, confluence_url: Optional[str], space_key: Optional[str], updated_by: Optional[str]) -> bool:
    url = f"{settings.kb_publisher_service}/api/ingestor/confluence/unsync"
    payload = {
        "collection_id": collection_id,
        "page_id": page_id,
        "confluence_url": confluence_url,
        "space_key": space_key,
        "updated_by": updated_by or "nexus-docs-admin"
    }
    logger.info("Curl to KB: " + url + " with payload: " + str(payload))
    response = requests.post(
        url,
        headers={"Accept": "application/json"},
        json=payload
    )
        
    if not response:
        logger.error("Failed to unsync from KB")
        raise Exception("Failed to unsync from KB")
    
    if response.status_code != 200:
        if response.json():
            logger.error(f"Failed to unsync from KB: {response.json()}")
        else:
            logger.error("Failed to unsync from KB: Empty response")
        return False
        
    logger.info(f"Unsynced from KB page {confluence_url} - {space_key} - {page_id} successfully")
    return True