from fastapi import FastAPI, Request
import uvicorn

app = FastAPI()


@app.post("/api/v1/search-engine/ingest")
async def mock_index(request: Request):
    data = await request.json()
    print("\n📥 Mock API Received Payload:")
    print(data)
    return {"message": "Mock index received successfully", "status": "ok"}
@app.delete("/api/v1/search-engine/documents/{document_id}")
async def mock_delete(request: Request):
    document_id = request.path_params.get("document_id")
    print("\n📥 Mock API Delete Request:")
    print(f"Deleting document with ID: {document_id}")
    return {"message": f"Document {document_id} deleted successfully", "status": "ok"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=10000)
