from math import log
import requests
from requests.auth import HTTPBasic<PERSON>uth
from dotenv import load_dotenv
import os
import logging
import aiohttp

logger = logging.getLogger(__name__)

load_dotenv(override=True)

CONFLUENCE_URL = os.getenv("CONFLUENCE_URL")
CONFLUENCE_TOKEN = os.getenv("CONFLUENCE_TOKEN")
CONFLUENCE_EMAIL = os.getenv("CONFLUENCE_EMAIL")

def get_confluence_page(page_id):
    url = f"{CONFLUENCE_URL}/wiki/rest/api/content/{page_id}?expand=body.export_view,version.by,history.createdBy,history.ownedBy,ancestors,space,metadata.labels"
    response = requests.get(
        url,
        auth=HTTPBasicAuth(CONFLUENCE_EMAIL, CONFLUENCE_TOKEN),
        headers={"Accept": "application/json"}
    )
    
    if response.status_code == 200:
        logger.info(f"Get page {page_id} from Confluence")
        logger.debug(f"Get page {page_id} from Confluence: {response.json()}")
        return response.json()
    else:
        logger.error(f"Failed to get page {page_id} from Confluence with status code {response.status_code}")
        return None

async def get_labels_by_page_id(page_id) -> set[str]:
    """
    Get all labels for a specific Confluence page
    
    Args:
        page_id: The ID of the Confluence page
        
    Returns:
        Set of label names or empty set if no labels or error
    """
    url = f"{CONFLUENCE_URL}/wiki/rest/api/content/{page_id}/label"
    try:
        logger.info(f"Retrieving labels for page {url}")
        
        async with aiohttp.ClientSession() as session:
            auth = aiohttp.BasicAuth(CONFLUENCE_EMAIL, CONFLUENCE_TOKEN)
            async with session.get(url, auth=auth, headers={"Accept": "application/json"}) as response:
                if response.status == 200:
                    logger.info(f"Retrieved labels for page {page_id}")
                    try:
                        response_data = await response.json()
                        
                        # Validate response structure
                        if not isinstance(response_data, dict) or "results" not in response_data:
                            raise Exception(f"Invalid response format for page {page_id} labels")
                            
                        # Extract labels safely
                        labels = []
                        for label in response_data.get("results", []):
                            if isinstance(label, dict) and "name" in label:
                                labels.append(label["name"])
                            else:
                                logger.warning(f"Skipping invalid label format in page {page_id}: {label}")
                        
                        logger.info(f"Labels for page {page_id}: {labels}")
                        return set(labels)
                    except ValueError as e:
                        raise Exception(f"Failed to parse JSON response for page {page_id} labels")
                else:
                    raise Exception(f"Failed to get labels for page {page_id} with status code {response.status}")
    except Exception as e:
        logger.error(f"Error getting labels for page {page_id}: {str(e)}")
        raise e
