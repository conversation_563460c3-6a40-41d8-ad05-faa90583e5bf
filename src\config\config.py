import os
from pydantic import Field
from pydantic_settings import BaseSettings
from typing import Optional, List

class Settings(BaseSettings):
    # App settings
    upload_folder: str = Field(default="uploads")
    html_folder: str = Field(default="html")
    app_port: int = Field(default=5000)
    
    # Logging settings
    log_level: str = Field(default="INFO")
    enable_file_log: bool = Field(default=True)
    
    # Database settings
    postgres_user: str = Field(env="POSTGRES_USER")
    postgres_password: str = Field(env="POSTGRES_PASSWORD")
    postgres_host: str = Field(env="POSTGRES_HOST")
    postgres_port: str = Field(env="POSTGRES_PORT")
    postgres_db: str = Field(env="POSTGRES_DB")
    
    # MinIO settings
    minio_endpoint: str = Field(env="MINIO_ENDPOINT")
    minio_access_key: str = Field(env="MINIO_ACCESS_KEY")
    minio_secret_key: str = Field(env="MINIO_SECRET_KEY")
    minio_bucket_name: str = Field(env="MINIO_BUCKET_NAME")
    minio_secure: bool = Field(env="MINIO_SECURE")
    minio_public_endpoint: str = Field(env="MINIO_PUBLIC_ENDPOINT")
    minio_public_bucket_name: str = Field(env="MINIO_PUBLIC_BUCKET_NAME")
    
    # Confluence settings
    confluence_url: Optional[str] = Field(env="CONFLUENCE_URL")
    confluence_token: Optional[str] = Field(env="CONFLUENCE_TOKEN")
    confluence_email: Optional[str] = Field(env="CONFLUENCE_EMAIL")
    enable_confluence_webhook: bool = Field(default=False, env="ENABLE_CONFLUENCE_WEBHOOK")
    confluence_webhook_secret: Optional[str] = Field(default=None, env="CONFLUENCE_WEBHOOK_SECRET")
    confluence_space_key: Optional[str] = Field(default=None, env="CONFLUENCE_SPACE_KEY")
    confluence_space_name: Optional[str] = Field(default=None, env="CONFLUENCE_SPACE_NAME")
    confluence_space_id: Optional[str] = Field(default="553844786", env="CONFLUENCE_SPACE_ID")
    
    # Knowledge Base settings
    kb_publisher_service: Optional[str] = Field(env="KB_PUBLISHER_SERVICE")
    kb_collection_id: Optional[str] = Field(env="KB_COLLECTION_ID")
    nexus_docs_public_url: str = Field(env="NEXUS_DOCS_PUBLIC_URL")

    # Full-text search settings, add default value
    full_text_search_service: str = Field(env="FULL_TEXT_SEARCH_SERVICE")
    
    # CORS settings
    cors_allow_origins: str = Field(
        default="https://dev-nexus-build.vnggames.net,http://localhost:3000",
        env="CORS_ALLOW_ORIGINS"
    )
    cors_allow_credentials: bool = Field(default=True, env="CORS_ALLOW_CREDENTIALS")
    cors_allow_methods: str = Field(
        default="GET,POST,OPTIONS,DELETE",
        env="CORS_ALLOW_METHODS"
    )
    cors_allow_headers: str = Field(
        default="Authentication,Content-Type,Authorization,Accept,Origin,X-Requested-With,Access-Control-Request-Method,Access-Control-Request-Headers",
        env="CORS_ALLOW_HEADERS"
    )
    
    # Cache settings
    cache_time: int = Field(default=300, env="CACHE_TIME")
    
    # Timezone settings
    timezone: str = Field(default="Asia/Ho_Chi_Minh", env="TZ")
    
    # Webhook settings
    enable_confluence_page_webhook: bool = Field(default=True, env="ENABLE_CONFLUENCE_PAGE_WEBHOOK")
    enable_confluence_label_webhook: bool = Field(default=False, env="ENABLE_CONFLUENCE_LABEL_WEBHOOK")
    
    def __init__(self, **data):
        super().__init__(**data)
        
        # Process string lists from environment variables
        if isinstance(self.cors_allow_origins, str):
            self.cors_allow_origins = self.cors_allow_origins.split(",")
            
        if isinstance(self.cors_allow_methods, str):
            self.cors_allow_methods = self.cors_allow_methods.split(",")
            
        if isinstance(self.cors_allow_headers, str):
            self.cors_allow_headers = self.cors_allow_headers.split(",")
    
    class Config:
        env_file = os.path.join(os.path.dirname(__file__), "..", ".env")
        case_sensitive = False

settings = Settings()
