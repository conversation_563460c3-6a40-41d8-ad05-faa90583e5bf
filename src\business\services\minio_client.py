import os
from minio import Minio
from dotenv import load_dotenv
from bs4 import BeautifulSoup 
from dotenv import load_dotenv
import requests
from urllib.parse import urlparse
import io
import uuid
from datetime import datetime, timezone
import asyncio

load_dotenv()  # Đ<PERSON>m bảo biến môi trường được load

class MockMinioClient:
    """Mock MinIO client for testing without a real MinIO server"""
    def __init__(self):
        self.objects = {}
        print("Using MockMinioClient instead of real MinIO")
    
    def bucket_exists(self, bucket_name):
        return bucket_name in self.objects
    
    def make_bucket(self, bucket_name):
        self.objects[bucket_name] = {}
        print(f"Created mock bucket: {bucket_name}")
    
    def put_object(self, bucket_name, object_name, data, length, content_type=None, metadata=None):
        if bucket_name not in self.objects:
            self.objects[bucket_name] = {}
        
        # Read the data if it's a file-like object
        if hasattr(data, 'read'):
            content = data.read()
        else:
            content = data
            
        self.objects[bucket_name][object_name] = {
            'content': content,
            'metadata': metadata or {},
            'content_type': content_type
        }
        print(f"Saved mock object: {bucket_name}/{object_name}")
    
    def get_object(self, bucket_name, object_name):
        if bucket_name not in self.objects or object_name not in self.objects[bucket_name]:
            raise Exception(f"Object {bucket_name}/{object_name} not found")
        
        obj = self.objects[bucket_name][object_name]
        
        # Create a file-like object
        if isinstance(obj['content'], bytes):
            data = io.BytesIO(obj['content'])
        else:
            data = io.BytesIO(obj['content'].encode('utf-8'))
        
        # Add necessary methods to mimic MinIO response
        data.release_conn = lambda: None
        data.close = lambda: None
        
        return data

def create_minio_client():
    
    # Original implementation for real MinIO
    endpoint = os.getenv('MINIO_ENDPOINT', 'localhost:9000')
    access_key = os.getenv('MINIO_ACCESS_KEY', 'minioadmin')
    secret_key = os.getenv('MINIO_SECRET_KEY', 'minioadmin123')
    secure = os.getenv('MINIO_SECURE', 'False').lower() == 'true'
    
    print(f"Connecting to MinIO at {endpoint} (secure={secure})")
    
    try:
        client = Minio(
            endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=secure
        )

        bucket_name = os.getenv('MINIO_BUCKET_NAME', 'mybucket')
        if not client.bucket_exists(bucket_name):
            client.make_bucket(bucket_name)
            print(f"Created bucket: {bucket_name}")
        else:
            print(f"Bucket already exists: {bucket_name}")

        return client
    except Exception as e:
        print(f"Error connecting to MinIO: {str(e)}")
        raise


async def save_file_html_to_minio_with_meta(html: str, minio_client, bucket: str):
    import io
    import uuid
    from datetime import datetime

    filename = f"{uuid.uuid4().hex[:8]}.html"
    file_data = io.BytesIO(html.encode('utf-8'))
    file_size = file_data.getbuffer().nbytes

    now_str = datetime.now(timezone.utc).isoformat()

    await asyncio.to_thread (minio_client.put_object)(
        bucket_name=bucket,
        object_name=filename,
        data=file_data,
        length=file_size,
        content_type="text/html",
        metadata={
            "created_date": now_str,
            "updated_date": now_str
        }
    )

    return filename, {
        "uri": filename,
        "content_type": "text/html",
        "created_date": now_str,
        "updated_date": now_str
    }
