from sqlalchemy import Column, Integer, String, DateTime, Text, Index
from sqlalchemy.orm import declarative_base
from datetime import datetime, timezone
from data.database import Base
from core.common.time_util import get_current_date, get_current_vn_date
from sqlalchemy import Boolean
# Base class for SQLAlchemy models
# index: (page_id, title), (parent_id, page_id), html_uri
class Page(Base):
    __tablename__ = 'pages'
    id = Column(Integer, primary_key=True)
    page_id = Column(String, nullable=False) # dang xac dinh khoa hay khong
    title = Column(String)
    type = Column(String, nullable=False) # page, folder
    is_published = Column(Boolean, default=False)
    content_type = Column(String, nullable=True)
    parent_id = Column(String)
    hash_content = Column(String, nullable=True)
    space_id = Column(String, nullable=True)
    html_uri = Column(String, nullable=True)
    created_by = Column(String)  # Author of the page
    status = Column(String, nullable=True) # code handle
    last_updated_by = Column(String, nullable=True)
    last_approved_date = Column(DateTime, nullable=True)
    created_date = Column(DateTime, default=get_current_date)
    updated_date = Column(DateTime, default=get_current_date)
    
    # Indexes for efficient querying
    __table_args__ = (
        Index('idx_page_id_title', page_id, title),  # Composite index for page_id and title
        Index('idx_parent_page_id', parent_id, page_id),  # Hierarchical index for parent-child queries
        Index('idx_html_uri', html_uri),  # Index for URI lookups
    )

class Space(Base):
    __tablename__ = 'spaces'
    id = Column(Integer, primary_key=True)
    space_id = Column(String, nullable=False, comment="Space ID")
    space_key = Column(String, nullable=False, comment="Space key")
    space_name = Column(String, nullable=False, comment="Space name")
    description = Column(String, nullable=True, comment="Space description")
    confluence_url = Column(String, nullable=True, comment="Confluence URL")
    confluence_email = Column(String, nullable=True, comment="Confluence email")
    confluence_token = Column(String, nullable=True, comment="Confluence token")
    ref_url = Column(String, nullable=True, comment="URL to redirect to when clicking on the space")
    background_url = Column(String, nullable=True, comment="Background URL")
    webhook_secret = Column(String, nullable=True, comment="Webhook secret")
    collection_id = Column(String, nullable=True, comment="Collection ID")
    active = Column(Boolean, default=True, comment="Active status")
    created_date = Column(DateTime, default=get_current_date, comment="Created date")
    updated_date = Column(DateTime, default=get_current_date, comment="Updated date")

    __table_args__ = (
        Index('idx_space_id_space_key', space_id, space_key),
        Index('idx_space_key', space_key)
    )

class Mgmnt(Base):
    __tablename__ = 'mgmnt'
    id = Column(Integer, primary_key=True)
    user_id = Column(String, nullable=False)  # User ID
    page_id = Column(String, nullable=False)  # Page ID
    status = Column(String)  # Status of the management action
    created_date = Column(DateTime, default=get_current_date())  # Creation date
    updated_date = Column(DateTime, default=get_current_date())  # Last update date

class FetchingStatus(Base):
    __tablename__ = 'fetching_status'
    id = Column(Integer, primary_key=True)
    user_id = Column(String, nullable=False)  # User ID
    status = Column(String)  # Status of the fetching action
    created_date = Column(DateTime, default=get_current_date())  # Creation date
    updated_date = Column(DateTime, default=get_current_date())  # Last update date