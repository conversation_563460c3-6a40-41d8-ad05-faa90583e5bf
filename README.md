# 📘 Markdown Publisher API

Flask API nhận nội dung Markdown, ch<PERSON><PERSON><PERSON> sang HTML, lưu thông tin vào cơ sở dữ liệu SQL (SQLite) và tích hợp với Confluence qua REST API.

---

## 🚀 Tính năng

- Nhận nội dung Markdown qua API
- Chuyể<PERSON> Markdown sang HTML
- Lưu thông tin trang vào cơ sở dữ liệu SQL (SQLite)
- Tích hợp với Atlassian Confluence (REST API)
- Hỗ trợ xác thực bằng Bearer Token hoặc Basic Auth

---

## 🗄️ Lưu trữ SQL (SQLite)

Ứng dụng sử dụng SQLite để lưu thông tin page. Bạn có thể dễ dàng chuyển sang các hệ quản trị cơ sở dữ liệu SQL khác như MySQL, PostgreSQL nếu cần.

### Cấu trú<PERSON> bảng `Page`
| Trường         | Kiểu dữ liệu | Ý nghĩa                         |
| -------------- | -----------  | ------------------------------- |
| id             | Integer      | Khóa chính, tự tăng             |
| title          | String       | Tiêu đề trang                   |
| html_uri       | String       | Đường dẫn HTML                  |
| status         | String       | Trạng thái (mặc định: active)   |
| created_date   | DateTime     | Ngày tạo                        |
| updated_date   | DateTime     | Ngày cập nhật                   |

### Khởi tạo database
- Khi chạy lần đầu, hệ thống sẽ tự động tạo file `pages.db` (hoặc bạn có thể thay đổi tên/loại DB trong cấu hình).
- Đảm bảo đã cài đặt SQLAlchemy:
  ```bash
  pip install sqlalchemy
  ```
- Ví dụ khởi tạo bảng:
  ```python
  from sqlalchemy import create_engine
  from src.models.page import Base
  engine = create_engine('sqlite:///pages.db')
  Base.metadata.create_all(engine)
  ```

---

## 🧩 Cấu trúc API

### `POST /api/publish`

Gửi nội dung Markdown để xử lý và lưu vào database.

#### Request headers:

