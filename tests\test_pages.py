from fastapi.testclient import TestClient
import sys, os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src')))
from main import app

client = TestClient(app)

def test_list_pages():
    response = client.get("/api/list-pages")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_get_page_not_found():
    response = client.get("/api/content/nonexistent-page-id")
    assert response.status_code == 404
