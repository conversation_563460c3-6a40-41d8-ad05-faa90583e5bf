from fastapi import Request, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError
from typing import Optional, Dict, Any, Set
import hashlib
import hmac
import asyncio
import os
import time
import logging
import json
from business.services.page_service import page_service
from fastapi import APIRouter
import traceback
from config.config import settings
from typing import List

from data.repositories import space_repository
from data.db_connection import get_session
router = APIRouter()

logger = logging.getLogger(__name__)
ENABLE_CONFLUENCE_WEBHOOK = os.getenv("ENABLE_CONFLUENCE_WEBHOOK", "false").lower() in ["true", "1", "t"]
WEBHOOK_SECRET = settings.confluence_webhook_secret

# Pydantic models for request validation
class ConfluenceUser(BaseModel):
    accountId: str
    displayName: str
    email: Optional[str] = None

class ConfluencePage(BaseModel):
    idAsString: Optional[str] = None
    creatorAccountId: Optional[str] = None
    spaceKey: Optional[str] = None
    spaceId: Optional[int] = None
    modificationDate: Optional[int] = None
    lastModifierAccountId: Optional[str] = None
    self: Optional[str] = None
    id: Optional[str] = None
    title: Optional[str] = None
    creationDate: Optional[int] = None
    contentType: Optional[str] = None
    version: Optional[int] = None

class ConfluenceWebhookEvent(BaseModel):
    page: Optional[ConfluencePage] = None
    userAccountId: Optional[str] = None
    accountType: Optional[str] = None
    timestamp: Optional[int] = None
    updateTrigger: Optional[str] = None
    suppressNotifications: Optional[bool] = None

class LabelItem(BaseModel):
    name: str

class LabeledPage(BaseModel):
    title: Optional[str] = None
    creationDate: Optional[int] = None
    version: Optional[int] = None
    labels: List[LabelItem] = []
    idAsString: Optional[str] = None
    creatorAccountId: Optional[str] = None
    spaceKey: Optional[str] = None
    spaceId: Optional[int] = None
    modificationDate: Optional[int] = None
    lastModifierAccountId: Optional[str] = None
    self: Optional[str] = None
    id: Optional[str] = None
    contentType: Optional[str] = None

class Label(BaseModel):
    name: str
    self: Optional[str] = None
    title: Optional[str] = None
    ownerAccountId: Optional[str] = None

class LabelDataEvent(BaseModel):
    labeled: LabeledPage
    label: Label
    userAccountId: Optional[str] = None
    accountType: Optional[str] = None
    timestamp: Optional[int] = None

def verify_webhook_signature(payload: bytes, signature: str, secret: str) -> bool:
    """Verify webhook signature for security"""
    if not signature or not secret:
        return False
    
    try:
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        return hmac.compare_digest(f"sha256={expected_signature}", signature)
    except Exception:
        return False
    
async def process_confluence_event(event_data: ConfluenceWebhookEvent) -> None:
    """Process the Confluence webhook event asynchronously"""
    try:
        event_type = event_data.updateTrigger
        
        spaceId = event_data.page.spaceId if event_data.page else None
        
        if spaceId is None:
            logger.warning(f"Skip processing event for space {spaceId} because space not found")
            return
        
        # validate space if active
        with get_session() as db:
            if not space_repository.is_active(db, str(spaceId)):
                logger.warning(f"Skip processing event for space {spaceId} because space is not active")
                return
        
        if event_type == "edit_page":
            await handle_page_updated(event_data)
        else:
            # this is page created
            await handle_page_created(event_data)
            
    except Exception as e:
        logger.exception(f"Error processing Confluence event {event_data.updateTrigger}: {str(e)}")
        # Could add retry logic or dead letter queue here

async def process_confluence_event_delete(event_data: ConfluenceWebhookEvent) -> None:
    """Process the Confluence webhook event asynchronously"""
    try:    
        spaceId = event_data.page.spaceId if event_data.page else None
        
        if spaceId is None:
            logger.warning(f"Skip processing event for space {spaceId} because space not found")
            return
        
        # validate space if active
        with get_session() as db:
            if not space_repository.is_active(db, str(spaceId)):
                logger.warning(f"Skip processing event for space {spaceId} because space is not active")
                return
    
        # safe condition delete page: if event_data before updated_date -> wrong
        page_id = event_data.page.id
        if page_id is None:
            logger.warning(f"Skip processing event for page {page_id} because page not found")
            return
        # TODO: check timestamp before delete page
        await handle_page_removed(event_data)
            
    except Exception as e:
        logger.error(f"Error processing Confluence event {event_data.updateTrigger}: {str(e)}")
        # Could add retry logic or dead letter queue here

async def handle_page_created(event_data: ConfluenceWebhookEvent) -> None:
    """Handle page creation events"""
    logger.info(f"Page created: {event_data.page.title} by {event_data.userAccountId}")
    if ENABLE_CONFLUENCE_WEBHOOK:
        # Trước tiên thực hiện publish_page để cập nhật DB và MinIO
        try:
            logger.info(f"check page infomation: contentType is {event_data.page.contentType}")
            await page_service.access_page_with_labels(event_data.page.id)
            logger.info(f"Handle page created successfully")
        except Exception as e:
            logger.error(f"Error in page creation handling: {str(e)}\nTraceback: {traceback.format_exc()}")

async def handle_page_updated(event_data: ConfluenceWebhookEvent) -> None:
    """Handle page update events"""
    logger.info(f"Page updated: {event_data.page.title} by {event_data.userAccountId}")
    if ENABLE_CONFLUENCE_WEBHOOK:
        # Thực hiện publish_page trước để đảm bảo dữ liệu được cập nhật
        try:
            logger.info(f"check page infomation: contentType is {event_data.page.contentType}")
            await page_service.access_page_with_labels(event_data.page.id)
            logger.info(f"Handle page updated successfully")
        except Exception as e:
            logger.error(f"Error in page update handling: {str(e)}\nTraceback: {traceback.format_exc()}")

async def handle_page_removed(event_data: ConfluenceWebhookEvent) -> None:
    """Handle page removal events"""
    logger.info(f"Page removed: {event_data.page.title} by {event_data.userAccountId}")
    if ENABLE_CONFLUENCE_WEBHOOK:
        try:
            # Xóa trang từ database trước
            await asyncio.to_thread(page_service.delete_page, event_data.page.id)
        except Exception as e:
            logger.exception(f"Error in page removal handling: {str(e)}\nTraceback: {traceback.format_exc()}")

async def process_label_event(labeled: LabeledPage, page_id: str):
    """
    Label behavior rules:

    - 'synckb' → Sync the document to the Knowledge Base. ✅ Normal
    - 'removekb' → Remove the document from the Knowledge Base. ✅ Normal
    - 'publish' → Make the document visible on the portal. ✅ Normal
    - 'unpublish' → Hide the document from the portal. ✅ Normal

    Valid combinations:
    - 'synckb' + 'publish' → Sync to KB and show on portal. ✅ Normal
    - 'removekb' + 'unpublish' → Remove from KB and hide from portal. ✅ Normal

    Conflict combinations (not allowed):
    - 'synckb' + 'removekb' → Conflict: can't sync and remove at the same time. ❌ Critical
    - 'publish' + 'unpublish' → Conflict: can't show and hide at the same time. ❌ Critical
    - All 4 labels → Total conflict: both KB and publish actions are contradictory. ❌ Critical
    """

    spaceId = labeled.spaceId if labeled else None
    
    if spaceId is None:
        logger.warning(f"Skip processing event for space {spaceId} because space not found")
        return
    
    # validate space if active
    with get_session() as db:
        if not space_repository.is_active(db, str(spaceId)):
            logger.warning(f"Skip processing event for space {spaceId} because space is not active")
            return

    logger.info(f"Processing label event for page {page_id} with labels: {labeled.labels}")
    label_set : Set[str] = {label.name for label in labeled.labels}
    
    if len(label_set) == 0:
        raise HTTPException(status_code=400, detail="No labels present")
    # Handle conflicts
    if {"synckb", "removekb", "publish", "unpublish"}.issubset(label_set):
        raise HTTPException(status_code=400, detail="Total conflict: all 4 labels present (synckb + removekb + publish + unpublish)")
    if {"synckb", "removekb"}.issubset(label_set):
        raise HTTPException(status_code=400, detail="Conflict: can't sync and remove at the same time (synckb + removekb)")
    if {"publish", "unpublish"}.issubset(label_set):
        raise HTTPException(status_code=400, detail="Conflict: can't publish and unpublish at the same time (publish + unpublish)")

    try:
        # Valid two-label combos
        if {"synckb", "publish"}.issubset(label_set):   
            await asyncio.to_thread(page_service.publish_page, page_id)
            logger.info(f"Document {page_id} published successfully")
            await asyncio.to_thread(page_service.sync_page, page_id)
            logger.info(f"Document {page_id} synced to KB successfully")
            return JSONResponse(status_code=200, content={"message": "Document synced to KB and published successfully"})
        
        if {"removekb", "unpublish"}.issubset(label_set):
            await asyncio.to_thread(page_service.unpublish_page, page_id)
            logger.info(f"Document {page_id} unpublished successfully")
            await asyncio.to_thread(page_service.unsync_page, page_id)
            logger.info(f"Document {page_id} removed from KB successfully")
            return JSONResponse(status_code=200, content={"message": "Document removed from KB and unpublished successfully"})
        
        if len(label_set) >= 2:
            logger.warning(f"Combination of labels is not allowed: {label_set}")
            raise HTTPException(status_code=400, detail="Combination of labels is not allowed")
        
        # Single-label actions
        if {"synckb"}.issubset(label_set):
            await asyncio.to_thread(page_service.sync_page, page_id)
            logger.info(f"Document {page_id} synced to KB successfully")
            return JSONResponse(status_code=200, content={"message": "Document synced to KB successfully"})
        
        if {"removekb"}.issubset(label_set):
            await asyncio.to_thread(page_service.unsync_page, page_id)
            logger.info(f"Document {page_id} removed from KB successfully")
            return JSONResponse(status_code=200, content={"message": "Document removed from KB successfully"})
        
        if {"publish"}.issubset(label_set):
            await asyncio.to_thread(page_service.publish_page, page_id)
            logger.info(f"Document {page_id} published successfully")
            return JSONResponse(status_code=200, content={"message": "Document published successfully"})
        
        if {"unpublish"}.issubset(label_set):
            await asyncio.to_thread(page_service.unpublish_page, page_id)
            logger.info(f"Document {page_id} unpublished successfully")
            return JSONResponse(status_code=200, content={"message": "Document unpublished successfully"})
        logger.warning(f"Other cases not supported: {label_set}")
        raise HTTPException(status_code=400, detail="Other cases not supported")
    
    except Exception as e:
        logger.error(f"Unexpected error processing labels {label_set} for page {page_id}: {str(e)}\nTraceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Failed to process label event")

@router.post("/webhook/confluence/events/delete")
async def handle_confluence_webhook_delete(
    request: Request, 
    background_tasks: BackgroundTasks
):
    """
    just receive event delete page
    """
    try:
        body = await request.body()
        logger.info(f"Received body: {body}")
        
        if not settings.enable_confluence_page_webhook:
            logger.warning("Confluence page webhook is disabled")
            return HTTPException(status_code=403, detail="Confluence page webhook is disabled")
        
        if WEBHOOK_SECRET:
            signature = request.headers.get("X-Hub-Signature-256")
            if not verify_webhook_signature(body=body, signature=signature, secret=WEBHOOK_SECRET):
                logger.warning("Invalid webhook signature")
                raise HTTPException(status_code=401, detail="Invalid signature")

        try:
            if not body:
                raise HTTPException(status_code=400, detail="Empty request body")
            raw_data = json.loads(body.decode('utf-8'))
            logger.info(f"Parsed JSON: {raw_data}")
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON payload: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        except UnicodeDecodeError as e:
            logger.error(f"Cannot decode body: {str(e)}")
            raise HTTPException(status_code=400, detail="Cannot decode body")
        try:
            event_data = ConfluenceWebhookEvent(**raw_data)
            if event_data.updateTrigger != "delete_page":
                event_data.updateTrigger = "delete_page"
            logger.info(f"Parsed event data: {event_data}")
        except ValidationError as e:
            logger.error(f"Invalid event data structure: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid event data structure")
            
        
        # Log the event for monitoring
        logger.info(
            f"Received Confluence webhook - Event: {event_data.updateTrigger}, "
            f"User: {event_data.userAccountId}, "
            f"Page: {event_data.page.title if event_data.page else 'N/A'}"
        )
        background_tasks.add_task(process_confluence_event_delete, event_data)
        
        # Trích xuất thông tin page từ event_data để trả về trong response
        page = event_data.page
        return JSONResponse(
                content={
                    "message": "Webhook received successfully",
                    "updateTrigger": event_data.updateTrigger,
                    "timestamp": event_data.timestamp,
                    "pageId": page.id if page else None,
                    "pageTitle": page.title if page else None,
                    "pageCreatorAccountId": page.creatorAccountId if page else None,
                    "pageSpaceKey": page.spaceKey if page else None,
                    "pageSpaceId": page.spaceId if page else None,
                    "pageModificationDate": page.modificationDate if page else None,
                    "pageLastModifierAccountId": page.lastModifierAccountId if page else None,
                    "pageSelf": page.self if page else None,
                    "pageCreationDate": page.creationDate if page else None,
                    "pageContentType": page.contentType if page else None,
                    "pageVersion": page.version if page else None,
                },
                status_code=200
            )
    except HTTPException:
        # Re-raise HTTP exceptions (these have appropriate status codes)
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
@router.post("/webhook/confluence/events")
async def handle_confluence_webhook(
    request: Request, 
    background_tasks: BackgroundTasks
):
    """
    Handle webhook events from Confluence
    
    This endpoint processes various Confluence events like page creation,
    updates, and deletions. Events are processed asynchronously in the background.
    """
    try:
        # Get raw body for signature verification
        body = await request.body()
        logger.info(f"Received body: {body}")
        
        # Verify webhook signature if secret is configured
        if WEBHOOK_SECRET:
            signature = request.headers.get("X-Hub-Signature-256")
            if not verify_webhook_signature(body=body, signature=signature, secret=WEBHOOK_SECRET):
                logger.warning("Invalid webhook signature")
                raise HTTPException(status_code=401, detail="Invalid signature")
        
        # Parse JSON from the body we already read
        try:
            if not body:
                raise HTTPException(status_code=400, detail="Empty request body")
            
            # Parse JSON from raw body instead of calling request.json()
            raw_data = json.loads(body.decode('utf-8'))
            logger.info(f"Parsed JSON: {raw_data}")
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON payload: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        except UnicodeDecodeError as e:
            logger.error(f"Cannot decode body: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid body encoding")
        
        # Validate event data structure
        try:
            event_data = ConfluenceWebhookEvent(**raw_data)
            if event_data.updateTrigger != "edit_page":
                event_data.updateTrigger = "create_page"
            logger.info(f"Parsed event data: {event_data}")
        except ValidationError as e:
            logger.error(f"Invalid event data structure: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid event data structure")
        
        # Log the event for monitoring
        logger.info(
            f"Received Confluence webhook - Event: {event_data.updateTrigger}, "
            f"User: {event_data.userAccountId}, "
            f"Page: {event_data.page.title if event_data.page else 'N/A'}"
        )
        
        # Process event in background to avoid blocking the response
        background_tasks.add_task(process_confluence_event, event_data)
        
        # Trích xuất thông tin page từ event_data để trả về trong response
        page = event_data.page
        
        return JSONResponse(
            content={
                "message": "Webhook received successfully",
                "updateTrigger": event_data.updateTrigger,
                "timestamp": event_data.timestamp,
                "pageId": page.id if page else None,
                "pageTitle": page.title if page else None,
                "pageCreatorAccountId": page.creatorAccountId if page else None,
                "pageSpaceKey": page.spaceKey if page else None,
                "pageSpaceId": page.spaceId if page else None,
                "pageModificationDate": page.modificationDate if page else None,
                "pageLastModifierAccountId": page.lastModifierAccountId if page else None,
                "pageSelf": page.self if page else None,
                "pageCreationDate": page.creationDate if page else None,
                "pageContentType": page.contentType if page else None,
                "pageVersion": page.version if page else None,
            },
            status_code=200
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions (these have appropriate status codes)
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Optional: Add a health check endpoint
@router.get("/api/v1/nexus-docs/webhook/confluence/health")
async def confluence_webhook_health():
    """Health check endpoint for the Confluence webhook"""
    return JSONResponse(
        content={
            "status": "healthy",
            "service": "confluence-webhook",
            "timestamp": int(time.time())
        },
        status_code=200
    )

# Optional: Add a health check endpoint
@router.post("/webhook/confluence/events/labels")
async def handle_label_event(event_data: LabelDataEvent, request: Request):
    
    logger.info(f"Received label event: {event_data}")
    
    if not settings.enable_confluence_label_webhook:
        logger.warning("Confluence label webhook is disabled")
        return HTTPException(status_code=403, detail="Confluence label webhook is disabled")
    
    try:
        if WEBHOOK_SECRET:
            body = await request.body()
            signature = request.headers.get("X-Hub-Signature-256")
            if not verify_webhook_signature(body=body, signature=signature, secret=WEBHOOK_SECRET):
                logger.warning("Invalid webhook signature")
                raise HTTPException(status_code=401, detail="Invalid signature")
        logger.info(f"Processing label event for page {event_data.labeled.id} with labels: {event_data.labeled.labels}")
        await process_label_event(event_data.labeled, page_id=event_data.labeled.id)
        return JSONResponse(status_code=200, content={"message": "Label event processed successfully"})
    except HTTPException as e:
        logger.warning(f"Error processing label event: {str(e)}")
        raise e
    except Exception as e:
        logger.exception(f"Error processing label event: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
