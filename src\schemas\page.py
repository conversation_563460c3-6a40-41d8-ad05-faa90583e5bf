from pydantic import BaseModel, HttpUrl
from datetime import datetime
from typing import Optional

class PageBase(BaseModel):
    title: str
    html_uri: str
    status: str = "active"

class PageCreate(PageBase):
    created_date: datetime
    updated_date: datetime

class PageUpdate(BaseModel):
    title: Optional[str] = None
    html_uri: Optional[str] = None
    status: Optional[str] = None

class PageInDB(PageBase):
    id: int
    created_date: datetime
    updated_date: datetime

    class Config:
        from_attributes = True

class PageResponse(PageInDB):
    pass
