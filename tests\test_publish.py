import sys
import os
from unittest.mock import patch, MagicMock

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src')))

os.environ['MINIO_ENDPOINT'] = 'localhost:9000'
os.environ['MINIO_ACCESS_KEY'] = 'minioadmin'
os.environ['MINIO_SECRET_KEY'] = 'minioadmin'
os.environ['MINIO_BUCKET_NAME'] = 'mybucket'
os.environ['DATABASE_URL'] = 'sqlite:///./test.db'

with patch('business.services.minio_client.create_minio_client') as mock_create_minio_client:
    mock_minio_client = MagicMock()
    mock_bucket = 'mybucket'
    mock_create_minio_client.return_value = (mock_minio_client, mock_bucket)

    from main import app
    from fastapi.testclient import TestClient

client = TestClient(app)

def test_publish_endpoint():
    response = client.post(
        "/api/publish",
        data={
            "token": "fake-token",
            "email": "<EMAIL>",
            "domain": "example.atlassian.net",
            "page_id": "12345"
        }
    )
    assert response.status_code in (200, 400, 500)
