# mock bucket same page
from business.services.minio_client import save_file_html_to_minio_with_meta
from data.repositories.page_repository2 import get_page_by_page_id, update_page
# Sử dụng module mới
from data.db_connection import get_session, init_db, close_session
from datetime import datetime
from business.services.minio_client import create_minio_client
# mock 20 pages
with get_session() as db:
    for i in range(1, 51):
        page = get_page_by_page_id(db, str(i))
        if not page:
            continue
        html_content = """<html><body><h1>Page {}</h1></body></html>""".format(i) # html
        minio_client, bucket = create_minio_client()
        html_uri, _ = save_file_html_to_minio_with_meta(html_content, minio_client, bucket)
        # change postgres html_uri
        update_page(db, page_id=str(i), html_uri=html_uri)
        print(f"Page {i} saved to Min<PERSON> successfully.")
