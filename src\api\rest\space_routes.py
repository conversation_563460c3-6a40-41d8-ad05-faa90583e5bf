from fastapi import APIRouter, HTTPException, Body, Query, Path
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime

from data.db_connection import get_session
import data.repositories.space_repository as space_repository
from data.repositories.dtos import SpaceDTO
import logging
from data.repositories.models import Space

logger = logging.getLogger(__name__)

router = APIRouter()


# Định nghĩa các models để validation request/response
class SpaceResponse(BaseModel):
    id: int
    space_id: str
    space_key: str
    space_name: str
    collection_id: Optional[str] = None
    confluence_url: Optional[str] = None
    background_url: Optional[str] = None
    class Config:
        orm_mode = True


class SpaceCreate(BaseModel):
    space_id: str
    space_key: str
    space_name: str
    collection_id: Optional[str] = None
    confluence_url: Optional[str] = None
    confluence_email: Optional[str] = None
    confluence_token: Optional[str] = None
    ref_url: Optional[str] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    background_url: Optional[str] = None


class SpaceUpdate(BaseModel):
    space_key: Optional[str] = None
    space_name: Optional[str] = None
    collection_id: Optional[str] = None
    confluence_url: Optional[str] = None
    confluence_email: Optional[str] = None
    confluence_token: Optional[str] = None
    ref_url: Optional[str] = None
    updated_by: Optional[str] = None
    background_url: Optional[str] = None


@router.get('/api/spaces', response_model=List[SpaceResponse])
async def get_all_spaces():
    """
    Lấy danh sách tất cả các spaces
    """
    try:
        with get_session() as db:
            spaces: List[Space] = space_repository.get_all_spaces(db)
        
        # Chuyển đổi đối tượng SQLAlchemy thành dictionaries
        spaces_data = [
            {
                "id": space.id,
                "space_id": space.space_id,
                "space_key": space.space_key,
                "space_name": space.space_name,
                "collection_id": space.collection_id,
                "confluence_url": space.confluence_url,
                "background_url": space.background_url
            } for space in spaces
        ]
        return [SpaceResponse.model_validate(space_dict) for space_dict in spaces_data]
    except Exception as e:
        logger.exception(f"Error getting all spaces: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting spaces: {str(e)}")

