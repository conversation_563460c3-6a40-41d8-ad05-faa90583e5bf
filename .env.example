APP_PORT=5000

# CORS Configuration
CORS_ALLOW_ORIGINS=https://dev-nexus-build.vnggames.net,http://localhost:3000
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,OPTIONS,DELETE
CORS_ALLOW_HEADERS=Authentication,Content-Type,Authorization,Accept,Origin,X-Requested-With,Access-Control-Request-Method,Access-Control-Request-Headers

# Minio
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=mybucket
MINIO_SECURE=False

MINIO_PUBLIC_ENDPOINT=localhost:9000
MINIO_PUBLIC_BUCKET_NAME=public

NEXUS_DOCS_PUBLIC_URL=http://localhost:5000

# Postgres
POSTGRES_USER=user
POSTGRES_PASSWORD=password
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=mydb

# Confluence config
CONFLUENCE_URL=
CONFLUENCE_TOKEN=
CONFLUENCE_EMAIL=

ENABLE_CONFLUENCE_WEBHOOK=false
CONFLUENCE_WEBHOOK_SECRET=

TZ=Asia/Ho_Chi_Minh

# Cache Configuration
CACHE_TIME=300

# Knowledge Base
KB_PUBLISHER_SERVICE=
KB_COLLECTION_ID=

# Logging
LOG_LEVEL=DEBUG
ENABLE_FILE_LOG=true

# Full-text search
FULL_TEXT_SEARCH_SERVICE=
