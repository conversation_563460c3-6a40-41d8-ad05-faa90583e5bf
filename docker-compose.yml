services:
  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - backend
    
  postgres:
    image: postgres:latest
    container_name: postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: mydb
      POSTGRES_HOST: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - backend
      
  confluece-mock:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: confluece-mock
    command: python mock_confluence.py
    ports:
      - "8000:8000"
    networks:
      - backend

  markdown-publisher:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: markdown-publisher
    ports:
      - "5000:5000"
    environment:
      APP_PORT: 5000
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      MINIO_BUCKET_NAME: mybucket
      MINIO_SECURE: False
      MINIO_PUBLIC_ENDPOINT: http://localhost:9000
      MINIO_PUBLIC_BUCKET_NAME: public
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_DB: mydb
      LOG_LEVEL: INFO
      ENABLE_FILE_LOG: false
      CONFLUENCE_URL: ${CONFLUENCE_URL}
      CONFLUENCE_TOKEN: ${CONFLUENCE_TOKEN}
      CONFLUENCE_EMAIL: ${CONFLUENCE_EMAIL}
      CACHE_TIME: 300
      NEXUS_DOCS_PUBLIC_URL: http://localhost:3000/aawp/nexus-docs?pages={page_id}
      ENABLE_CONFLUENCE_WEBHOOK: true
      CONFLUENCE_WEBHOOK_SECRET: 
      TZ: Asia/Ho_Chi_Minh
      KB_PUBLISHER_SERVICE: localhost:8000
      KB_COLLECTION_ID: 1875847584
      CORS_ALLOW_ORIGINS: http://localhost:3000
      CORS_ALLOW_CREDENTIALS: true
      CORS_ALLOW_METHODS: GET,POST,OPTIONS,DELETE
      CORS_ALLOW_HEADERS: Authentication,Content-Type,Authorization,Accept,Origin,X-Requested-With,Access-Control-Request-Method,Access-Control-Request-Headers
      FULL_TEXT_SEARCH_SERVICE: https://dev-aawp.vnggames.net
      CONFLUENCE_SPACE_KEY:   
    depends_on:
      - minio
      - postgres
    networks:
      - backend

volumes:
  postgres_data:
  minio_data:

networks:
  backend:
    driver: bridge
    name: nexus_document_backend

