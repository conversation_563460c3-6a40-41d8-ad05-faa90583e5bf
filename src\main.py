from fastapi import FastAP<PERSON>, Request
from starlette.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
import logging 
import uvicorn
import logging

# Sử dụng module mới
from data.db_connection import init_db as init_postgres_db
from api.rest.page_routes import router as page_router
from api.rest.markdown_routes import router as markdown_router
from api.rest.user_routes import router as user_router
from api.rest.webhook_route import router as webhook_router
from api.rest.space_routes import router as space_router
from business.services.minio_client import create_minio_client
from business.services.page_service import page_service

from config.config import settings
from core.logging_config import setup_logging

setup_logging()

logger = logging.getLogger(__name__)

load_dotenv()

CORS_ALLOW_ORIGINS = os.getenv('CORS_ALLOW_ORIGINS', 'https://dev-nexus-build.vnggames.net,http://localhost:3000').split(',')
CORS_ALLOW_CREDENTIALS = os.getenv("CORS_ALLOW_CREDENTIALS", "true")
CORS_ALLOW_METHODS = os.getenv('CORS_ALLOW_METHODS', 'GET,POST,OPTIONS,DELETE').split(',')
CORS_ALLOW_HEADERS = os.getenv('CORS_ALLOW_HEADERS', 'Authentication,Content-Type,Authorization,Accept,Origin,X-Requested-With,Access-Control-Request-Method,Access-Control-Request-Headers').split(',')

def create_app():
    # Define context path for documentation
    context_path = "/api/v1/nexus-docs"
    
    app = FastAPI(
        title="Markdown Publisher API",
        description="API for publishing Markdown content to HTML and storing in database",
        version="1.0.0",
        # Set the OpenAPI URL and docs URL to include the context path
        openapi_url=f"{context_path}/openapi.json",
        docs_url=f"{context_path}/docs",
        redoc_url=f"{context_path}/redoc"
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=CORS_ALLOW_ORIGINS,
        allow_credentials=CORS_ALLOW_CREDENTIALS,
        allow_methods=CORS_ALLOW_METHODS,
        allow_headers=CORS_ALLOW_HEADERS,
    )
    
    # Khởi tạo biến môi trường / config
    app.state.upload_folder = settings.upload_folder
    app.state.html_folder = settings.html_folder

    # Khởi tạo PostgreSQL DB schema
    init_postgres_db()

    # Middleware ghi log request
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        logger = logging.getLogger("uvicorn.access")
        logger.info(f"→ Request: {request.method} {request.url}")
        try:
            response = await call_next(request)
        except Exception as e:
            logger.exception("Unhandled error occurred during request")
            raise
        logger.info(f"← Response: {response.status_code} for {request.method} {request.url}")
        return response
    
    app.include_router(page_router, prefix=context_path)
    app.include_router(markdown_router, prefix=context_path)
    app.include_router(webhook_router, prefix=context_path)
    app.include_router(space_router, prefix=context_path)
    return app

app = create_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=int(settings.app_port), reload=True)
