from fastapi import APIRouter, Request, Form, HTTPException, Depends, Body
from fastapi import BackgroundTasks
from fastapi.responses import JSONResponse
from business.services.confluence import get_confluence_page
from business.services.html_process import save_processed_html_to_minio
from sqlalchemy.orm import Session
# Sử dụng module mới
from data.db_connection import get_session
from data.repositories.models import Page as PostgresPage
from pydantic import BaseModel, field_validator
from datetime import datetime, timezone
import traceback
from business.services.kb_client import sync_to_kb
import logging
from typing import Optional, Dict
from business.services.page_service import page_service
from typing import List
import json
import asyncio
from config.config import settings
from data.repositories.models import Space

logger = logging.getLogger(__name__)

class PublishResponse(BaseModel):
    message: str
    file_uri: str
    hash_content: Optional[str] = None
    content_type: Optional[str] = None
    created_date: Optional[datetime] = None
    updated_date: Optional[datetime] = None
    page_id: str
    owner: Optional[str] = None
    last_updated_time: Optional[str] = None
    title: Optional[str] = None
    last_updated_by: Optional[str] = None

class ApproveRequest(BaseModel):
    page_id: str
    space_key: str
    @field_validator("page_id")
    def validate_page_id(value):
        if not value:
            raise ValueError("Page ID is required")
        return value

class ApproveResponse(BaseModel):
    message: str
    updated_date: datetime
    page_id: str

router = APIRouter()

@router.post("/api/publish", response_model=PublishResponse)
async def publish_md(
    request: Request,
    background_tasks: BackgroundTasks,
    page_id: str = Form(..., description="Confluence page ID to be published"),
    type: str = Form("html", description="Type of page to be published: markdown or html"),
    cookies_raw: Optional[str] = Form(None, description="Cookies as JSON string", examples=[""]),
):
    """
    Publish a Confluence page to the system.
    
    This endpoint fetches a page from Confluence, processes its HTML content,
    stores it in MinIO object storage, and saves metadata to PostgreSQL database.
    
    Returns:
        PublishResponse: Contains message confirmation, file URI, content, dates, and page ID
    
    Raises:
        HTTPException: If the page cannot be fetched from Confluence or if there's a database error
    """
    # Validate input early
    if type not in ["html", "markdown"]:
        raise HTTPException(status_code=400, detail="Invalid content type. Must be 'html' or 'markdown'")
    
    cookies = None
    if cookies_raw:
        try:
            cookies = json.loads(cookies_raw)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid cookies format (not valid JSON)")
    try:
        logger.info(f"Fetched page from Confluence: {page_id}")
        
        result = await asyncio.to_thread(page_service.publish_page, page_id, type, cookies)

        logger.info(f"Invalidated cache tree pages")
        return PublishResponse(
            message=result.get("message", ""),
            file_uri=result.get("file_uri", ""),
            hash_content=result.get("hash_content", ""),
            content_type=result.get("content_type", type),
            created_date=result.get("created_date", ""),
            updated_date=result.get("updated_date", ""),
            page_id=result.get("page_id", ""),
            owner=result.get("owner", "owner"),
            last_updated_time=result.get("last_updated_time", "last_updated_time"),
            title=result.get("title", "NOT_FOUND"),
            last_updated_by=result.get("last_updated_by", "last_updated_by")
        )
        
    except HTTPException as e:
        logger.error(f"HTTP error processing page {page_id}: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error processing page {page_id}: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to process page: {str(e)}")

@router.post("/api/approve", response_model=ApproveResponse)
async def approve_page(
    approve_request: ApproveRequest
):
    """
    Approve a Confluence page to Knowledge Base
    
    This endpoint updates the status of a Confluence page to "approved" in the PostgreSQL database.
    
    Returns:
        ApproveResponse: Contains message confirmation, updated date, and page ID
    
    Raises:
        HTTPException: If the page cannot be fetched from Confluence or if there's a database error
    """
    try:
        
        result = await asyncio.to_thread(page_service.sync_page, approve_request.page_id)

        return ApproveResponse(
            message=result.get("message", ""),
            updated_date=result.get("updated_date", ""),
            page_id=result.get("page_id", ""),
        )
    except Exception as e:
        logger.error(f"Failed to approve page: {str(e)}\n {traceback.format_exc()}")
        raise HTTPException(status_code=400, detail=f"Failed to approve page: {str(e)}")