from sqlalchemy.orm import Session
from data.repositories.models import Space
from datetime import datetime
from typing import List, Optional
import logging
from data.repositories.dtos import SpaceDTO, SpaceCreateDTO

logger = logging.getLogger(__name__)


def _space_to_dto(space: Space) -> SpaceDTO:
    """
    Convert a Space model instance to SpaceDTO
    
    Args:
        space: Space model instance
        
    Returns:
        SpaceDTO: Space data transfer object
    """
    return SpaceDTO(
        id=space.id,
        space_id=space.space_id,
        space_key=space.space_key,
        space_name=space.space_name,
        collection_id=space.collection_id,
        created_date=space.created_date,
        updated_date=space.updated_date
    )

def insert_space(db: Session, space_data: SpaceCreateDTO) -> int:
    """
    Insert a new space into the database
    
    Args:
        db: Database session
        space_data: Space creation data
        
    Returns:
        int: The ID of the inserted space
        
    Raises:
        Exception: If insertion fails
    """
    try:
        space = Space(**space_data)
        db.add(space)
        db.commit()
        db.refresh(space)
        return space.id
    except Exception as e:
        db.rollback()
        logger.exception(f"Error inserting space: {e}")
        raise e


def update_space(db: Session, space_id: str, **kwargs) -> int:
    """
    Update an existing space
    
    Args:
        db: Database session
        space_id: ID of the space to update
        **kwargs: Fields to update
        
    Returns:
        int: Number of rows affected
        
    Raises:
        Exception: If update fails
    """
    try:
        kwargs['updated_date'] = datetime.now()
        rows_affected = db.query(Space).filter(Space.space_id == space_id).update(kwargs)
        db.commit()
        return rows_affected
    except Exception as e:
        db.rollback()
        logger.exception(f"Error updating space {space_id}: {e}")
        raise e


def get_space_by_space_id(db: Session, space_id: str) -> Optional[Space]:
    """
    Get a space by its space_id
    
    Args:
        db: Database session
        space_id: ID of the space to retrieve
        
    Returns:
        Optional[Space]: Space data or None if not found
    """
    try:
        space = db.query(Space).filter(Space.space_id == space_id).first()
        if space:
            return space
        return None
    except Exception as e:
        logger.exception(f"Error getting space by space_id {space_id}: {e}")
        raise e


def get_space_by_id(db: Session, id: int) -> Optional[Space]:
    """
    Get a space by its primary key ID
    
    Args:
        db: Database session
        id: Primary key ID of the space
        
    Returns:
        Optional[Space]: Space data or None if not found
    """
    try:
        space = db.query(Space).filter(Space.id == id).first()
        if space:
            return space
        return None
    except Exception as e:
        logger.exception(f"Error getting space by id {id}: {e}")
        raise e

def get_space_by_space_key(db: Session, space_key: str) -> Optional[Space]:
    """
    Get a space by its space_key
    
    Args:
        db: Database session
        space_key: Space key of the space
        
    Returns:
        Optional[Space]: Space data or None if not found
    """
    try:
        space = db.query(Space).filter(Space.space_key == space_key).first()
        if space:
            return space
        return None
    except Exception as e:
        logger.exception(f"Error getting space by space_key {space_key}: {e}")
        raise e


def is_space_exists(db: Session, space_id: str) -> bool:
    """
    Check if a space exists by space_id
    
    Args:
        db: Database session
        space_id: ID of the space to check
        
    Returns:
        bool: True if space exists, False otherwise
    """
    try:
        return db.query(Space).filter(Space.space_id == space_id).count() > 0
    except Exception as e:
        logger.exception(f"Error checking if space exists {space_id}: {e}")
        raise e

def is_active(db: Session, space_id: str) -> bool:
    try:
        space = db.query(Space).filter(Space.space_id == space_id).first()
        if space:
            return space.active
        logger.warning(f"Space {space_id} not found. Default return False")
        return False
    except Exception as e:
        logger.exception(f"Error checking if space is active {space_id}: {e}")
        raise e


def delete_space(db: Session, space_id: str) -> int:
    """
    Delete a space by space_id
    
    Args:
        db: Database session
        space_id: ID of the space to delete
        
    Returns:
        int: Number of rows affected
        
    Raises:
        Exception: If deletion fails
    """
    try:
        rows_affected = db.query(Space).filter(Space.space_id == space_id).delete()
        db.commit()
        return rows_affected
    except Exception as e:
        db.rollback()
        logger.exception(f"Error deleting space {space_id}: {e}")
        raise e


def get_all_spaces(db: Session) -> List[Space]:
    """
    Get all spaces
    
    Args:
        db: Database session
        
    Returns:
        List[Space]: List of space data objects
    """
    try:
        query = db.query(Space).order_by(Space.space_name)
        
        spaces = query.all()
        
        return [
            space
            for space in spaces
        ]
    except Exception as e:
        logger.exception(f"Error getting all spaces: {e}")
        raise e


def search_spaces_by_name(db: Session, search_term: str, limit: Optional[int] = None) -> List[Space]:
    """
    Search spaces by name (case-insensitive partial match)
    
    Args:
        db: Database session
        search_term: Term to search for in space names
        limit: Maximum number of results to return
        
    Returns:
        List[SpaceDTO]: List of matching space data objects
    """
    try:
        query = db.query(Space).filter(
            Space.space_name.ilike(f"%{search_term}%")
        ).order_by(Space.space_name)
        
        if limit is not None:
            query = query.limit(limit)
            
        spaces = query.all()
        
        return [
            space
            for space in spaces
        ]
    except Exception as e:
        logger.exception(f"Error searching spaces by name '{search_term}': {e}")
        raise e


def get_spaces_count(db: Session) -> int:
    """
    Get the total count of spaces
    
    Args:
        db: Database session
        
    Returns:
        int: Total number of spaces
    """
    try:
        return db.query(Space).count()
    except Exception as e:
        logger.exception(f"Error getting spaces count: {e}")
        raise e


def get_spaces_by_collection_id(db: Session, collection_id: str) -> List[Space]:
    """
    Get all spaces with a specific collection_id
    
    Args:
        db: Database session
        collection_id: Collection ID to filter by
        
    Returns:
        List[SpaceDTO]: List of space data objects with matching collection_id
    """
    try:
        spaces = db.query(Space).filter(
            Space.collection_id == collection_id
        ).order_by(Space.space_name).all()
        
        return [
            space
            for space in spaces
        ]
    except Exception as e:
        logger.exception(f"Error getting spaces by collection_id '{collection_id}': {e}")
        raise e